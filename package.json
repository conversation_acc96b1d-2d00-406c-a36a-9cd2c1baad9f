{"name": "trodoo-vibe-demo", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro check && astro build", "preview": "astro preview", "astro": "astro", "check": "astro check"}, "dependencies": {"@astrojs/alpinejs": "^0.4.0", "@astrojs/check": "^0.9.4", "@astrojs/node": "^8.3.4", "@astrojs/react": "^3.6.2", "@astrojs/tailwind": "^5.1.2", "@nanostores/react": "^1.0.0", "@radix-ui/react-slot": "^1.2.3", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "alpinejs": "^3.14.7", "astro": "^4.16.18", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "lucide-react": "^0.522.0", "meilisearch": "^0.51.0", "nanostores": "^1.0.1", "pocketbase": "^0.21.5", "react": "^18.3.1", "react-day-picker": "^9.7.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2"}, "devDependencies": {"@types/alpinejs": "^3.13.10"}}