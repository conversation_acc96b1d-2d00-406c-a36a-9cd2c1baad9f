---
import Layout from '../../components/core/Layout.astro';
---

<Layout 
  title="Browse Venues - Trodoo"
  description="Discover unique venues for your special events. From intimate gatherings to grand celebrations."
>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-16 text-center">
          <h1 class="text-4xl font-bold text-gray-900 mb-4">
            Find Your Perfect Venue
          </h1>
          <p class="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover unique spaces for your special events. From intimate gatherings to grand celebrations.
          </p>
        </div>
      </div>
    </div>

    <!-- Search Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label for="location" class="block text-sm font-medium text-gray-700 mb-2">Location</label>
            <input 
              type="text" 
              id="location" 
              placeholder="Enter city or address"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <label for="capacity" class="block text-sm font-medium text-gray-700 mb-2">Capacity</label>
            <select 
              id="capacity"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Any size</option>
              <option value="1-50">1-50 guests</option>
              <option value="51-100">51-100 guests</option>
              <option value="101-200">101-200 guests</option>
              <option value="200+">200+ guests</option>
            </select>
          </div>
          <div>
            <label for="price" class="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
            <select 
              id="price"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Any price</option>
              <option value="0-100">$0-$100/hr</option>
              <option value="101-250">$101-$250/hr</option>
              <option value="251-500">$251-$500/hr</option>
              <option value="500+">$500+/hr</option>
            </select>
          </div>
          <div class="flex items-end">
            <button 
              type="button"
              class="w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Search Venues
            </button>
          </div>
        </div>
      </div>

      <!-- Venues Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Placeholder venue cards -->
        {[1, 2, 3, 4, 5, 6].map(i => (
          <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div class="h-48 bg-gray-200 flex items-center justify-center">
              <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="p-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Sample Venue {i}</h3>
              <p class="text-sm text-gray-600 mb-2">123 Main Street, City</p>
              <p class="text-sm text-gray-700 mb-3">Beautiful venue perfect for your special event...</p>
              <div class="flex justify-between items-center">
                <span class="text-lg font-semibold text-primary-600">$150/hr</span>
                <button class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm hover:bg-primary-700">
                  View Details
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      <!-- Empty State (when no venues found) -->
      <div class="hidden text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No venues found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
      </div>
    </div>
  </div>
</Layout>

<script>
  // Search functionality placeholder
  document.addEventListener('DOMContentLoaded', () => {
    const searchButton = document.querySelector('button[type="button"]');
    if (searchButton) {
      searchButton.addEventListener('click', () => {
        // Placeholder for search functionality
        console.log('Search venues functionality will be implemented here');
      });
    }
  });
</script>
